{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 11859885955182220028, "deps": [[40386456601120721, "percent_encoding", false, 2382831823201976880], [654232091421095663, "tauri_utils", false, 2619348071071930130], [1200537532907108615, "url<PERSON><PERSON>n", false, 6978942217447047142], [1967864351173319501, "muda", false, 8733984419453387237], [2013030631243296465, "webview2_com", false, 9323343060120079412], [3150220818285335163, "url", false, 13667360580885977189], [3331586631144870129, "getrandom", false, 17181548631031968536], [4143744114649553716, "raw_window_handle", false, 13865258021938575219], [4919829919303820331, "serialize_to_javascript", false, 1807659079117182590], [5986029879202738730, "log", false, 14541755981667113291], [8569119365930580996, "serde_json", false, 16172838625782452234], [9010263965687315507, "http", false, 6691494122375815684], [9689903380558560274, "serde", false, 11379971524019555881], [10229185211513642314, "mime", false, 13817454924497094945], [10806645703491011684, "thiserror", false, 17730430230988731617], [11989259058781683633, "dunce", false, 10772715448290900052], [12092653563678505622, "build_script_build", false, 37361732768056031], [12304025191202589669, "tauri_runtime_wry", false, 4522427438118836785], [12565293087094287914, "window_vibrancy", false, 12667469767875518], [12943761728066819757, "tauri_runtime", false, 226691605838431166], [12944427623413450645, "tokio", false, 17902613235780410147], [12986574360607194341, "serde_repr", false, 10165930562763555643], [13077543566650298139, "heck", false, 6968490215517245970], [13405681745520956630, "tauri_macros", false, 483622074230285619], [13625485746686963219, "anyhow", false, 558503839760976789], [14585479307175734061, "windows", false, 6776533110835285877], [16928111194414003569, "dirs", false, 1763291190330950240], [17155886227862585100, "glob", false, 8317046001852949071]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-4c0f06fc30e76d38\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}