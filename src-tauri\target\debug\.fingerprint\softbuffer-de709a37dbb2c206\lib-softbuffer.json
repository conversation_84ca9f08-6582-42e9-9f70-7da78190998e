{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 1590280634380056766, "deps": [[376837177317575824, "build_script_build", false, 17472522930416798531], [4143744114649553716, "raw_window_handle", false, 13865258021938575219], [5986029879202738730, "log", false, 14541755981667113291], [10281541584571964250, "windows_sys", false, 12874691444620694038]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-de709a37dbb2c206\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}