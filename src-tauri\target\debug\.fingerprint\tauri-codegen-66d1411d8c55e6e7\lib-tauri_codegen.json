{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 6577188918832389190, "deps": [[654232091421095663, "tauri_utils", false, 17136263448484912294], [3060637413840920116, "proc_macro2", false, 1090526749411683902], [3150220818285335163, "url", false, 4112424237060816796], [4899080583175475170, "semver", false, 12887735383236162628], [4974441333307933176, "syn", false, 6118340939400336248], [7170110829644101142, "json_patch", false, 78893054188587611], [7392050791754369441, "ico", false, 14456309074338054570], [8319709847752024821, "uuid", false, 9671131774335477287], [8569119365930580996, "serde_json", false, 4192930890260707379], [9556762810601084293, "brotli", false, 10343763306422126802], [9689903380558560274, "serde", false, 871098456458781462], [9857275760291862238, "sha2", false, 9748554118050250873], [10806645703491011684, "thiserror", false, 17730430230988731617], [12687914511023397207, "png", false, 7324608214386478302], [13077212702700853852, "base64", false, 3409851990522453685], [15622660310229662834, "walkdir", false, 16825423647821545631], [17990358020177143287, "quote", false, 114483286961806686]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-66d1411d8c55e6e7\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}