{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 6542572997003217810, "deps": [[376837177317575824, "softbuffer", false, 3886806989134445737], [654232091421095663, "tauri_utils", false, 2619348071071930130], [2013030631243296465, "webview2_com", false, 9323343060120079412], [3150220818285335163, "url", false, 13667360580885977189], [3722963349756955755, "once_cell", false, 6491020715396783938], [4143744114649553716, "raw_window_handle", false, 13865258021938575219], [5986029879202738730, "log", false, 14541755981667113291], [8826339825490770380, "tao", false, 8078129340897128806], [9010263965687315507, "http", false, 6691494122375815684], [9141053277961803901, "wry", false, 7612374143885499046], [12304025191202589669, "build_script_build", false, 14517187151466590166], [12943761728066819757, "tauri_runtime", false, 226691605838431166], [14585479307175734061, "windows", false, 6776533110835285877]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-57892b29fbff3c42\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}