# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-icon-as-template"
description = "Enables the set_icon_as_template command without any pre-configured scope."
commands.allow = ["set_icon_as_template"]

[[permission]]
identifier = "deny-set-icon-as-template"
description = "Denies the set_icon_as_template command without any pre-configured scope."
commands.deny = ["set_icon_as_template"]
