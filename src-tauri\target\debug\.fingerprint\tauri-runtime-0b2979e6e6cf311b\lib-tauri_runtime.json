{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 4927569773606711902, "deps": [[654232091421095663, "tauri_utils", false, 2619348071071930130], [3150220818285335163, "url", false, 13667360580885977189], [4143744114649553716, "raw_window_handle", false, 13865258021938575219], [7606335748176206944, "dpi", false, 14648532118398371025], [8569119365930580996, "serde_json", false, 16172838625782452234], [9010263965687315507, "http", false, 6691494122375815684], [9689903380558560274, "serde", false, 11379971524019555881], [10806645703491011684, "thiserror", false, 17730430230988731617], [12943761728066819757, "build_script_build", false, 1010849262817595003], [14585479307175734061, "windows", false, 6776533110835285877], [16727543399706004146, "cookie", false, 9398790338508640301]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-0b2979e6e6cf311b\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}