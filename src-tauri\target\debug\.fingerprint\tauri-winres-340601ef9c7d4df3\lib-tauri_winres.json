{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 2225463790103693989, "path": 12998061747668285241, "deps": [[6493259146304816786, "indexmap", false, 18183956446048359510], [6941104557053927479, "embed_resource", false, 3039538466830360761], [15609422047640926750, "toml", false, 3466371346542570823]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-340601ef9c7d4df3\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}