{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 4078347620279405210, "deps": [[654232091421095663, "tauri_utils", false, 17136263448484912294], [2704937418414716471, "tauri_codegen", false, 9875093723308270274], [3060637413840920116, "proc_macro2", false, 1090526749411683902], [4974441333307933176, "syn", false, 6118340939400336248], [13077543566650298139, "heck", false, 6968490215517245970], [17990358020177143287, "quote", false, 114483286961806686]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-1835c2802ae8cc00\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}