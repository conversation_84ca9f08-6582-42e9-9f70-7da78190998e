{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 5112595333477610608, "deps": [[654232091421095663, "tauri_utils", false, 17136263448484912294], [4824857623768494398, "cargo_toml", false, 17440280081095168626], [4899080583175475170, "semver", false, 12887735383236162628], [6913375703034175521, "schemars", false, 831723526984072884], [7170110829644101142, "json_patch", false, 78893054188587611], [8569119365930580996, "serde_json", false, 4192930890260707379], [9689903380558560274, "serde", false, 871098456458781462], [12714016054753183456, "tauri_winres", false, 17922033978213491388], [13077543566650298139, "heck", false, 6968490215517245970], [13625485746686963219, "anyhow", false, 558503839760976789], [15609422047640926750, "toml", false, 3466371346542570823], [15622660310229662834, "walkdir", false, 16825423647821545631], [16928111194414003569, "dirs", false, 15846443792212521440], [17155886227862585100, "glob", false, 8317046001852949071]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-0e8cd65bb165af29\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}