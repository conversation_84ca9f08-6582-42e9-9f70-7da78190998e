{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 18229693946196339992, "deps": [[3150220818285335163, "url", false, 4112424237060816796], [6913375703034175521, "build_script_build", false, 17356233905178903263], [6982418085031928086, "dyn_clone", false, 6833832536924583174], [8319709847752024821, "uuid1", false, 9671131774335477287], [8569119365930580996, "serde_json", false, 4192930890260707379], [9689903380558560274, "serde", false, 871098456458781462], [14923790796823607459, "indexmap", false, 16182860485025993783], [16071897500792579091, "schemars_derive", false, 6365969911030953474]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-5be9156868d560a7\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}