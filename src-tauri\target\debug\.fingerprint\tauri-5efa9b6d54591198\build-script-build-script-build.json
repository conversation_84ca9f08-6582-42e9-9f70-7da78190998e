{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 4468085115773513420, "deps": [[654232091421095663, "tauri_utils", false, 17136263448484912294], [2021102184660760340, "tauri_build", false, 3782261380159000362], [13077543566650298139, "heck", false, 6968490215517245970], [17155886227862585100, "glob", false, 8317046001852949071]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-5efa9b6d54591198\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}