{"name": "moedownloader", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json --watch", "tauri": "tauri", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "license": "MIT", "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@lucide/svelte": "^0.533.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@tauri-apps/cli": "^2", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.0.1", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.6", "typescript": "~5.6.2", "vite": "^6.0.3", "vite-plugin-devtools-json": "^0.2.0"}}